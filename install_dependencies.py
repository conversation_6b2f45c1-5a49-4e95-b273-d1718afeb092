#!/usr/bin/env python3
"""
依赖安装脚本：提供多种安装选项
"""

import subprocess
import sys
import platform
import os

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"\n{'='*50}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=False, text=True)
        print(f"✓ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠ 警告: 推荐使用Python 3.8或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
        return True

def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ 检测到CUDA GPU: {torch.cuda.get_device_name()}")
            return True
        else:
            print("⚠ 未检测到CUDA GPU，将使用CPU")
            return False
    except ImportError:
        print("? 无法检测GPU（torch未安装）")
        return None

def install_minimal():
    """安装最小依赖"""
    print("选择：最小依赖安装")
    return run_command("pip install -r requirements-minimal.txt", "安装最小依赖")

def install_full():
    """安装完整依赖"""
    print("选择：完整依赖安装")
    return run_command("pip install -r requirements.txt", "安装完整依赖")

def install_cpu_only():
    """仅CPU版本安装"""
    print("选择：CPU版本安装")
    commands = [
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu",
        "pip install transformers>=4.37.0",
        "pip install numpy>=1.20.0"
    ]
    
    for i, cmd in enumerate(commands, 1):
        if not run_command(cmd, f"安装CPU依赖 ({i}/{len(commands)})"):
            return False
    return True

def install_cuda():
    """CUDA版本安装"""
    print("选择：CUDA版本安装")
    
    # 检测CUDA版本（如果可能）
    cuda_version = "cu118"  # 默认版本
    
    print(f"使用CUDA版本: {cuda_version}")
    
    commands = [
        f"pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/{cuda_version}",
        "pip install transformers>=4.37.0",
        "pip install numpy>=1.20.0"
    ]
    
    for i, cmd in enumerate(commands, 1):
        if not run_command(cmd, f"安装CUDA依赖 ({i}/{len(commands)})"):
            return False
    return True

def verify_installation():
    """验证安装"""
    print("\n" + "="*50)
    print("验证安装...")
    print("="*50)
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
        
        import numpy
        print(f"✓ NumPy {numpy.__version__}")
        
        # 检查CUDA
        if torch.cuda.is_available():
            print(f"✓ CUDA可用: {torch.cuda.get_device_name()}")
        else:
            print("⚠ CUDA不可用，使用CPU")
        
        print("\n✓ 所有依赖安装成功！")
        return True
        
    except ImportError as e:
        print(f"✗ 安装验证失败: {e}")
        return False

def main():
    """主函数"""
    print("QUBO转换实验 - 依赖安装脚本")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        print("请升级Python版本后重试")
        return
    
    # 显示系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    
    # 选择安装方式
    print("\n请选择安装方式:")
    print("1. 最小依赖安装 (推荐，自动处理依赖)")
    print("2. 完整依赖安装 (包含所有可选依赖)")
    print("3. CPU版本安装 (明确指定CPU版本)")
    print("4. CUDA版本安装 (如果有GPU)")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == '1':
                success = install_minimal()
                break
            elif choice == '2':
                success = install_full()
                break
            elif choice == '3':
                success = install_cpu_only()
                break
            elif choice == '4':
                success = install_cuda()
                break
            elif choice == '5':
                print("退出安装")
                return
            else:
                print("无效选择，请输入1-5")
                continue
                
        except KeyboardInterrupt:
            print("\n\n安装被中断")
            return
    
    # 验证安装
    if success:
        verify_installation()
        
        print("\n" + "="*50)
        print("下一步:")
        print("1. 运行环境检查: python check_env.py")
        print("2. 运行快速测试: python quick_test.py")
        print("3. 运行演示实验: python demo_experiment.py")
        print("4. 运行真实实验: python qubo_experiment.py")
        print("="*50)
    else:
        print("\n安装失败，请检查网络连接和错误信息")

if __name__ == "__main__":
    main()
