#!/usr/bin/env python3
"""
QUBO转换实验：测试Qwen2.5-Math-1.5B将数学问题转换为QUBO格式的能力
"""

import json
import random
import re
import os
from typing import List, Dict, Any, Tuple
import time

# 设置随机种子以确保可重现性
random.seed(42)

class QUBOExperiment:
    def __init__(self, model_path: str = "./Qwen2.5-Math-1.5B", data_path: str = "./eval_data/MATH.json"):
        self.model_path = model_path
        self.data_path = data_path
        self.model = None
        self.tokenizer = None
        self.results = []
        
    def load_model(self):
        """加载Qwen2.5-Math模型"""
        try:
            from transformers import AutoModelForCausalLM, AutoTokenizer
            import torch
            
            print("正在加载模型...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None
            )
            print(f"✓ 模型加载成功，设备: {next(self.model.parameters()).device}")
            return True
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            return False
    
    def create_qubo_prompt(self, math_problem: str) -> str:
        """创建QUBO转换的prompt"""
        prompt = f"""You are an expert in mathematical optimization and QUBO (Quadratic Unconstrained Binary Optimization) formulations.

Your task is to convert the given mathematical problem into a QUBO format. 

QUBO Format Requirements:
1. Variables must be binary (0 or 1)
2. The objective function must be quadratic in binary variables
3. All constraints must be incorporated into the objective function using penalty terms
4. Output format should be: minimize x^T Q x where Q is the coefficient matrix

Mathematical Problem:
{math_problem}

Please convert this problem to QUBO format. If the problem cannot be naturally converted to QUBO, explain why and suggest the closest QUBO approximation.

Your response should include:
1. Problem analysis
2. Variable definitions (what each binary variable represents)
3. QUBO formulation
4. Coefficient matrix Q (if possible)

QUBO Formulation:"""
        
        return prompt
    
    def generate_response(self, prompt: str, max_length: int = 1024) -> str:
        """使用模型生成响应"""
        try:
            inputs = self.tokenizer(prompt, return_tensors="pt")
            
            # 移动到模型设备
            if self.model.device != inputs.input_ids.device:
                inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_length,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应（只取新生成的部分）
            response = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            return f"生成失败: {str(e)}"
    
    def load_math_problems(self, num_problems: int = 15) -> List[Dict]:
        """从MATH数据集加载问题"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                all_problems = json.load(f)
            
            # 随机选择问题
            selected_problems = random.sample(all_problems, min(num_problems, len(all_problems)))
            print(f"✓ 已选择 {len(selected_problems)} 个数学问题")
            
            return selected_problems
            
        except Exception as e:
            print(f"✗ 加载数据失败: {e}")
            return []
    
    def analyze_qubo_response(self, response: str) -> Dict[str, Any]:
        """分析QUBO响应的质量"""
        analysis = {
            'has_variables': False,
            'has_objective': False,
            'has_matrix': False,
            'mentions_binary': False,
            'mentions_qubo': False,
            'format_score': 0,
            'content_analysis': ""
        }
        
        response_lower = response.lower()
        
        # 检查是否提到二进制变量
        if any(term in response_lower for term in ['binary', '0 or 1', '{0,1}', 'binary variable']):
            analysis['mentions_binary'] = True
            analysis['format_score'] += 1
        
        # 检查是否提到QUBO
        if 'qubo' in response_lower:
            analysis['mentions_qubo'] = True
            analysis['format_score'] += 1
        
        # 检查是否定义了变量
        if any(term in response_lower for term in ['variable', 'x_', 'let x', 'define']):
            analysis['has_variables'] = True
            analysis['format_score'] += 1
        
        # 检查是否有目标函数
        if any(term in response_lower for term in ['minimize', 'maximize', 'objective', 'x^t', 'quadratic']):
            analysis['has_objective'] = True
            analysis['format_score'] += 1
        
        # 检查是否有矩阵表示
        if any(term in response_lower for term in ['matrix', 'q =', 'coefficient']):
            analysis['has_matrix'] = True
            analysis['format_score'] += 1
        
        # 内容分析
        if analysis['format_score'] >= 4:
            analysis['content_analysis'] = "高质量QUBO转换"
        elif analysis['format_score'] >= 2:
            analysis['content_analysis'] = "部分QUBO元素"
        else:
            analysis['content_analysis'] = "缺乏QUBO结构"
        
        return analysis
    
    def run_experiment(self, num_problems: int = 15) -> Dict[str, Any]:
        """运行完整实验"""
        print("=== QUBO转换实验开始 ===")
        
        # 1. 加载模型
        if not self.load_model():
            return {"error": "模型加载失败"}
        
        # 2. 加载数学问题
        problems = self.load_math_problems(num_problems)
        if not problems:
            return {"error": "数据加载失败"}
        
        # 3. 执行转换实验
        results = []
        for i, problem in enumerate(problems, 1):
            print(f"\n--- 问题 {i}/{len(problems)} ---")
            print(f"原问题: {problem['question'][:100]}...")
            
            # 创建prompt并生成响应
            prompt = self.create_qubo_prompt(problem['question'])
            start_time = time.time()
            response = self.generate_response(prompt)
            generation_time = time.time() - start_time
            
            # 分析响应质量
            analysis = self.analyze_qubo_response(response)
            
            result = {
                'problem_id': problem['index'],
                'original_question': problem['question'],
                'original_answer': problem['answer'],
                'qubo_response': response,
                'analysis': analysis,
                'generation_time': generation_time
            }
            
            results.append(result)
            print(f"生成时间: {generation_time:.2f}s")
            print(f"质量评分: {analysis['format_score']}/5 ({analysis['content_analysis']})")
        
        # 4. 统计结果
        total_problems = len(results)
        high_quality = sum(1 for r in results if r['analysis']['format_score'] >= 4)
        medium_quality = sum(1 for r in results if 2 <= r['analysis']['format_score'] < 4)
        low_quality = sum(1 for r in results if r['analysis']['format_score'] < 2)
        
        avg_score = sum(r['analysis']['format_score'] for r in results) / total_problems
        avg_time = sum(r['generation_time'] for r in results) / total_problems
        
        summary = {
            'total_problems': total_problems,
            'high_quality_count': high_quality,
            'medium_quality_count': medium_quality,
            'low_quality_count': low_quality,
            'high_quality_rate': high_quality / total_problems,
            'average_score': avg_score,
            'average_generation_time': avg_time,
            'detailed_results': results
        }
        
        return summary

def main():
    """主函数"""
    experiment = QUBOExperiment()
    
    # 运行实验
    results = experiment.run_experiment(num_problems=15)
    
    if 'error' in results:
        print(f"实验失败: {results['error']}")
        return
    
    # 保存结果
    output_file = f"qubo_experiment_results_{int(time.time())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 实验结果总结 ===")
    print(f"总问题数: {results['total_problems']}")
    print(f"高质量转换: {results['high_quality_count']} ({results['high_quality_rate']:.1%})")
    print(f"中等质量转换: {results['medium_quality_count']}")
    print(f"低质量转换: {results['low_quality_count']}")
    print(f"平均质量评分: {results['average_score']:.2f}/5")
    print(f"平均生成时间: {results['average_generation_time']:.2f}秒")
    print(f"详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
