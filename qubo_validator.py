#!/usr/bin/env python3
"""
QUBO格式验证器：验证模型输出的QUBO格式是否正确
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Any, Optional

class QUBOValidator:
    """QUBO格式验证器"""
    
    def __init__(self):
        self.validation_results = {}
    
    def validate_qubo_response(self, response: str) -> Dict[str, Any]:
        """验证QUBO响应的完整性和正确性"""
        validation = {
            'format_valid': False,
            'has_binary_variables': False,
            'has_objective_function': False,
            'has_matrix_representation': False,
            'variable_definitions_clear': False,
            'mathematical_correctness': 'unknown',
            'extracted_components': {},
            'issues': [],
            'overall_score': 0
        }
        
        # 1. 检查二进制变量定义
        binary_check = self._check_binary_variables(response)
        validation['has_binary_variables'] = binary_check['found']
        validation['extracted_components']['variables'] = binary_check['variables']
        if not binary_check['found']:
            validation['issues'].append("缺少二进制变量定义")
        
        # 2. 检查目标函数
        objective_check = self._check_objective_function(response)
        validation['has_objective_function'] = objective_check['found']
        validation['extracted_components']['objective'] = objective_check['objective']
        if not objective_check['found']:
            validation['issues'].append("缺少目标函数")
        
        # 3. 检查矩阵表示
        matrix_check = self._check_matrix_representation(response)
        validation['has_matrix_representation'] = matrix_check['found']
        validation['extracted_components']['matrix'] = matrix_check['matrix']
        if not matrix_check['found']:
            validation['issues'].append("缺少矩阵表示")
        
        # 4. 检查变量定义清晰度
        var_def_check = self._check_variable_definitions(response)
        validation['variable_definitions_clear'] = var_def_check['clear']
        validation['extracted_components']['definitions'] = var_def_check['definitions']
        if not var_def_check['clear']:
            validation['issues'].append("变量定义不清晰")
        
        # 5. 数学正确性检查
        math_check = self._check_mathematical_correctness(validation['extracted_components'])
        validation['mathematical_correctness'] = math_check['status']
        if math_check['issues']:
            validation['issues'].extend(math_check['issues'])
        
        # 6. 计算总体评分
        validation['overall_score'] = self._calculate_overall_score(validation)
        validation['format_valid'] = validation['overall_score'] >= 0.6
        
        return validation
    
    def _check_binary_variables(self, response: str) -> Dict[str, Any]:
        """检查二进制变量定义"""
        result = {'found': False, 'variables': []}
        
        # 查找二进制变量的模式
        patterns = [
            r'x_?\d*\s*∈\s*\{0,\s*1\}',
            r'x_?\d*\s*\in\s*\{0,\s*1\}',
            r'binary\s+variable[s]?\s*:?\s*x_?\d*',
            r'x_?\d*\s*is\s+binary',
            r'let\s+x_?\d*\s*be\s+binary',
            r'x_?\d*\s*=\s*0\s+or\s+1'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                result['found'] = True
                result['variables'].extend(matches)
        
        # 查找变量列表
        var_patterns = [
            r'x_?(\d+)',
            r'variables?\s*:?\s*([x_\d,\s]+)'
        ]
        
        for pattern in var_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            result['variables'].extend(matches)
        
        return result
    
    def _check_objective_function(self, response: str) -> Dict[str, Any]:
        """检查目标函数"""
        result = {'found': False, 'objective': ''}
        
        # 查找目标函数的模式
        patterns = [
            r'minimize\s*:?\s*([^.\n]+)',
            r'maximize\s*:?\s*([^.\n]+)',
            r'objective\s*:?\s*([^.\n]+)',
            r'x\^?T\s*Q\s*x',
            r'∑.*x_?\d*.*x_?\d*'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.MULTILINE)
            if matches:
                result['found'] = True
                result['objective'] = matches[0] if isinstance(matches[0], str) else str(matches[0])
                break
        
        return result
    
    def _check_matrix_representation(self, response: str) -> Dict[str, Any]:
        """检查矩阵表示"""
        result = {'found': False, 'matrix': ''}
        
        # 查找矩阵的模式
        patterns = [
            r'Q\s*=\s*\[([^\]]+)\]',
            r'matrix\s*Q\s*:?\s*([^.\n]+)',
            r'coefficient\s+matrix\s*:?\s*([^.\n]+)',
            r'\[\s*\[.*\]\s*\]'  # 矩阵格式
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if matches:
                result['found'] = True
                result['matrix'] = matches[0]
                break
        
        return result
    
    def _check_variable_definitions(self, response: str) -> Dict[str, Any]:
        """检查变量定义的清晰度"""
        result = {'clear': False, 'definitions': []}
        
        # 查找变量定义的模式
        patterns = [
            r'x_?\d*\s*represents?\s*([^.\n]+)',
            r'let\s+x_?\d*\s*=\s*([^.\n]+)',
            r'x_?\d*\s*:\s*([^.\n]+)',
            r'where\s+x_?\d*\s*([^.\n]+)'
        ]
        
        definition_count = 0
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.MULTILINE)
            if matches:
                definition_count += len(matches)
                result['definitions'].extend(matches)
        
        # 如果找到至少一个变量定义，认为定义清晰
        result['clear'] = definition_count > 0
        
        return result
    
    def _check_mathematical_correctness(self, components: Dict) -> Dict[str, Any]:
        """检查数学正确性"""
        result = {'status': 'unknown', 'issues': []}
        
        # 基本一致性检查
        has_vars = bool(components.get('variables'))
        has_obj = bool(components.get('objective'))
        has_matrix = bool(components.get('matrix'))
        
        if has_vars and has_obj:
            result['status'] = 'likely_correct'
        elif has_vars or has_obj:
            result['status'] = 'partially_correct'
            result['issues'].append("QUBO组件不完整")
        else:
            result['status'] = 'incorrect'
            result['issues'].append("缺少基本QUBO组件")
        
        return result
    
    def _calculate_overall_score(self, validation: Dict) -> float:
        """计算总体评分"""
        score = 0.0
        
        # 各组件权重
        weights = {
            'has_binary_variables': 0.25,
            'has_objective_function': 0.30,
            'has_matrix_representation': 0.20,
            'variable_definitions_clear': 0.15,
            'mathematical_correctness': 0.10
        }
        
        # 二进制变量
        if validation['has_binary_variables']:
            score += weights['has_binary_variables']
        
        # 目标函数
        if validation['has_objective_function']:
            score += weights['has_objective_function']
        
        # 矩阵表示
        if validation['has_matrix_representation']:
            score += weights['has_matrix_representation']
        
        # 变量定义
        if validation['variable_definitions_clear']:
            score += weights['variable_definitions_clear']
        
        # 数学正确性
        math_status = validation['mathematical_correctness']
        if math_status == 'likely_correct':
            score += weights['mathematical_correctness']
        elif math_status == 'partially_correct':
            score += weights['mathematical_correctness'] * 0.5
        
        return score
    
    def generate_validation_report(self, validation: Dict) -> str:
        """生成验证报告"""
        report = []
        report.append("=== QUBO格式验证报告 ===")
        report.append(f"总体评分: {validation['overall_score']:.2f}/1.00")
        report.append(f"格式有效: {'是' if validation['format_valid'] else '否'}")
        report.append("")
        
        # 各项检查结果
        checks = [
            ('二进制变量定义', validation['has_binary_variables']),
            ('目标函数', validation['has_objective_function']),
            ('矩阵表示', validation['has_matrix_representation']),
            ('变量定义清晰', validation['variable_definitions_clear'])
        ]
        
        for check_name, passed in checks:
            status = "✓" if passed else "✗"
            report.append(f"{status} {check_name}")
        
        report.append(f"数学正确性: {validation['mathematical_correctness']}")
        
        # 问题列表
        if validation['issues']:
            report.append("\n发现的问题:")
            for issue in validation['issues']:
                report.append(f"- {issue}")
        
        # 提取的组件
        if validation['extracted_components']:
            report.append("\n提取的组件:")
            for component, content in validation['extracted_components'].items():
                if content:
                    report.append(f"- {component}: {str(content)[:100]}...")
        
        return "\n".join(report)

def validate_qubo_file(file_path: str) -> None:
    """验证文件中的QUBO响应"""
    import json
    
    validator = QUBOValidator()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'detailed_results' in data:
            results = data['detailed_results']
            
            print(f"验证 {len(results)} 个QUBO转换结果...")
            
            for i, result in enumerate(results, 1):
                print(f"\n--- 问题 {i} ---")
                validation = validator.validate_qubo_response(result['qubo_response'])
                report = validator.generate_validation_report(validation)
                print(report)
                
                # 更新结果
                result['validation'] = validation
            
            # 保存更新后的结果
            output_file = file_path.replace('.json', '_validated.json')
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"\n验证完成，结果保存到: {output_file}")
        
    except Exception as e:
        print(f"验证失败: {e}")

if __name__ == "__main__":
    # 示例用法
    sample_response = """
    To convert this problem to QUBO format:
    
    Variables: Let x_1, x_2 be binary variables where x_i ∈ {0,1}
    x_1 represents choice A, x_2 represents choice B
    
    Objective: minimize x_1^2 + 2*x_1*x_2 + x_2^2
    
    Matrix Q = [[1, 1], [1, 1]]
    """
    
    validator = QUBOValidator()
    validation = validator.validate_qubo_response(sample_response)
    report = validator.generate_validation_report(validation)
    print(report)
