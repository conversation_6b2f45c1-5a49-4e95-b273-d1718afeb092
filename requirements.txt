# QUBO转换实验依赖包
# 用法: pip install -r requirements.txt

# 核心深度学习框架
torch>=2.0.0
transformers>=4.37.0

# 数值计算和数据处理
numpy>=1.20.0

# 可选依赖（用于更好的性能和功能）
# 如果有CUDA GPU，可以安装对应版本的torch
# torch==2.1.0+cu118 --index-url https://download.pytorch.org/whl/cu118

# 用于加速tokenizer（可选）
tokenizers>=0.13.0

# 用于模型安全加载（通常随transformers自动安装）
safetensors>=0.3.0

# 用于进度条显示（通常随transformers自动安装）
tqdm>=4.64.0

# 用于配置文件处理（通常随transformers自动安装）
pyyaml>=6.0

# 用于HTTP请求（通常随transformers自动安装）
requests>=2.28.0

# 用于正则表达式（通常随transformers自动安装）
regex>=2022.7.9

# Hugging Face Hub集成（通常随transformers自动安装）
huggingface-hub>=0.15.0

# 文件锁定（通常随torch自动安装）
filelock>=3.8.0

# 类型扩展（通常随torch自动安装）
typing-extensions>=4.3.0

# 符号数学（通常随torch自动安装）
sympy>=1.11.0

# 网络分析（通常随torch自动安装）
networkx>=2.8.0

# 模板引擎（通常随torch自动安装）
jinja2>=3.1.0

# 文件系统抽象（通常随torch自动安装）
fsspec>=2022.8.0

# 包装工具（通常随torch自动安装）
packaging>=21.0
