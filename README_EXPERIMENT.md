# QUBO转换实验框架

## 实验目标
测试Qwen2.5-Math-1.5B模型在零样本（zero-shot）情况下，将数学问题转换为QUBO（Quadratic Unconstrained Binary Optimization）格式的能力。

## 文件结构

### 核心文件
- `qubo_experiment.py` - 主实验脚本
- `qubo_validator.py` - QUBO格式验证器
- `result_analyzer.py` - 结果分析器
- `check_env.py` - 环境检查脚本
- `quick_test.py` - 快速功能测试

### 数据文件
- `eval_data/MATH.json` - MATH数据集（5000个数学问题）
- `Qwen2.5-Math-1.5B/` - 预训练模型文件

### 输出文件
- `qubo_experiment_results_*.json` - 实验结果
- `*_report.html` - HTML格式的分析报告

## 使用方法

### 1. 环境检查
```bash
python check_env.py
```
检查Python环境、依赖包、模型文件和数据文件是否就绪。

### 2. 快速测试
```bash
python quick_test.py
```
运行基本功能测试，验证各组件是否正常工作。

### 3. 运行完整实验
```bash
python qubo_experiment.py
```
执行完整的QUBO转换实验，默认测试15个随机选择的数学问题。

### 4. 分析结果
```bash
python result_analyzer.py [结果文件名]
```
分析实验结果并生成详细报告。如果不指定文件名，会自动使用最新的结果文件。

### 5. 验证QUBO格式
```bash
python qubo_validator.py
```
单独验证QUBO响应的格式正确性。

## 实验设计

### 数据采样
- 从MATH数据集的5000个问题中随机选择15个
- 使用固定随机种子（42）确保可重现性
- 涵盖不同类型的数学问题

### Prompt设计
实验使用精心设计的prompt，包含：
1. 角色定义（数学优化专家）
2. QUBO格式要求说明
3. 输出格式规范
4. 具体任务指令

### 评估指标
1. **格式正确性**：
   - 是否提到二进制变量
   - 是否包含目标函数
   - 是否有矩阵表示
   - 变量定义是否清晰

2. **质量评分**（1-5分）：
   - 5分：完整的QUBO格式
   - 4分：大部分QUBO元素
   - 3分：部分QUBO结构
   - 2分：基本概念提及
   - 1分：缺乏QUBO理解

3. **性能指标**：
   - 生成时间
   - 成功率统计

## 实验结果分析

### 统计指标
- 高质量转换率（评分≥4）
- 中等质量转换率（评分2-3）
- 低质量转换率（评分<2）
- 平均质量评分
- 平均生成时间

### 详细分析
- 响应模式分析
- 问题类型效果对比
- 典型示例展示
- HTML可视化报告

## 预期挑战

1. **格式理解**：模型可能不完全理解QUBO的数学格式要求
2. **问题映射**：并非所有数学问题都能自然转换为QUBO
3. **约束处理**：将约束转换为惩罚项可能不准确
4. **矩阵表示**：生成正确的系数矩阵具有挑战性

## 技术要求

### 依赖包
```
torch >= 2.0.0
transformers >= 4.37.0
numpy >= 1.20.0
```

### 硬件要求
- 推荐：GPU（CUDA支持）
- 最低：CPU（运行较慢）
- 内存：至少8GB RAM

### Python版本
- Python 3.8+

## 实验配置

### 模型参数
- 模型：Qwen2.5-Math-1.5B
- 最大生成长度：1024 tokens
- 温度：0.7
- 采样：True

### 实验参数
- 测试问题数：15个
- 随机种子：42
- 超时设置：每个问题最多30秒

## 结果解读

### 成功指标
- 高质量转换率 > 30%：表现良好
- 平均评分 > 3.0：基本可用
- 格式正确率 > 50%：有一定理解

### 失败模式
- 完全不理解QUBO概念
- 输出格式混乱
- 无法处理约束条件
- 生成时间过长

## 后续改进方向

1. **Few-shot学习**：提供示例来改善性能
2. **Prompt优化**：调整prompt设计
3. **微调训练**：针对QUBO任务进行微调
4. **约束处理**：改进约束到惩罚项的转换
5. **验证增强**：添加数学正确性验证

## 注意事项

1. 实验结果仅反映零样本性能
2. QUBO转换的数学正确性需要人工验证
3. 生成时间受硬件配置影响
4. 某些数学问题本质上不适合QUBO格式

## 联系信息

如有问题或建议，请查看代码注释或运行相应的测试脚本。
