#!/usr/bin/env python3
"""
检查实验环境和依赖
"""

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = {
        'torch': 'PyTorch',
        'transformers': 'Hugging Face Transformers',
        'numpy': 'NumPy',
        'json': 'JSON (built-in)',
        'random': 'Random (built-in)',
        're': 'Regular Expressions (built-in)'
    }
    
    missing_packages = []
    installed_packages = {}
    
    for package, description in required_packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            installed_packages[package] = version
            print(f"✓ {description}: {version}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {description}: NOT INSTALLED")
    
    return missing_packages, installed_packages

def check_model_path():
    """检查模型路径"""
    import os
    model_path = "./Qwen2.5-Math-1.5B"
    
    if os.path.exists(model_path):
        print(f"✓ Model path exists: {model_path}")
        
        # 检查关键文件
        key_files = ['config.json', 'model.safetensors', 'tokenizer.json']
        for file in key_files:
            file_path = os.path.join(model_path, file)
            if os.path.exists(file_path):
                print(f"  ✓ {file}")
            else:
                print(f"  ✗ {file} missing")
        return True
    else:
        print(f"✗ Model path not found: {model_path}")
        return False

def check_data_path():
    """检查数据路径"""
    import os
    data_path = "./eval_data/MATH.json"
    
    if os.path.exists(data_path):
        print(f"✓ Data file exists: {data_path}")
        
        # 检查数据格式
        try:
            import json
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"  ✓ Data loaded successfully: {len(data)} problems")
            
            # 检查数据结构
            if data and isinstance(data[0], dict):
                keys = list(data[0].keys())
                print(f"  ✓ Data structure: {keys}")
                return True
            else:
                print("  ✗ Invalid data structure")
                return False
        except Exception as e:
            print(f"  ✗ Error loading data: {e}")
            return False
    else:
        print(f"✗ Data file not found: {data_path}")
        return False

if __name__ == "__main__":
    print("=== 环境检查 ===")
    
    print("\n1. 检查Python依赖:")
    missing, installed = check_dependencies()
    
    print("\n2. 检查模型文件:")
    model_ok = check_model_path()
    
    print("\n3. 检查数据文件:")
    data_ok = check_data_path()
    
    print("\n=== 总结 ===")
    if missing:
        print(f"需要安装的包: {', '.join(missing)}")
    else:
        print("✓ 所有依赖已安装")
    
    if model_ok and data_ok:
        print("✓ 环境检查通过，可以开始实验")
    else:
        print("✗ 环境检查失败，请解决上述问题")
