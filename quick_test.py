#!/usr/bin/env python3
"""
快速测试脚本：在依赖安装完成前进行基本功能测试
"""

import json
import random
import time

def load_sample_problems(data_path: str = "./eval_data/MATH.json", num_samples: int = 5):
    """加载样本问题"""
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            all_problems = json.load(f)
        
        # 随机选择问题
        random.seed(42)
        selected = random.sample(all_problems, min(num_samples, len(all_problems)))
        
        print(f"✓ 成功加载 {len(selected)} 个样本问题")
        return selected
    except Exception as e:
        print(f"✗ 加载数据失败: {e}")
        return []

def create_sample_prompt(problem: str) -> str:
    """创建样本prompt"""
    prompt = f"""You are an expert in mathematical optimization and QUBO (Quadratic Unconstrained Binary Optimization) formulations.

Your task is to convert the given mathematical problem into a QUBO format. 

QUBO Format Requirements:
1. Variables must be binary (0 or 1)
2. The objective function must be quadratic in binary variables
3. All constraints must be incorporated into the objective function using penalty terms
4. Output format should be: minimize x^T Q x where Q is the coefficient matrix

Mathematical Problem:
{problem}

Please convert this problem to QUBO format. If the problem cannot be naturally converted to QUBO, explain why and suggest the closest QUBO approximation.

Your response should include:
1. Problem analysis
2. Variable definitions (what each binary variable represents)
3. QUBO formulation
4. Coefficient matrix Q (if possible)

QUBO Formulation:"""
    
    return prompt

def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载 ===")
    problems = load_sample_problems(num_samples=3)
    
    if problems:
        print("\n样本问题:")
        for i, problem in enumerate(problems, 1):
            print(f"{i}. ID:{problem['index']} - {problem['question'][:80]}...")
            print(f"   答案: {problem['answer']}")
        return True
    return False

def test_prompt_generation():
    """测试prompt生成"""
    print("\n=== 测试Prompt生成 ===")
    
    sample_problem = "Find the maximum value of x + y subject to x^2 + y^2 ≤ 1."
    prompt = create_sample_prompt(sample_problem)
    
    print("生成的Prompt预览:")
    print(prompt[:300] + "...")
    print(f"Prompt长度: {len(prompt)} 字符")
    
    return True

def test_qubo_validator():
    """测试QUBO验证器"""
    print("\n=== 测试QUBO验证器 ===")
    
    try:
        from qubo_validator import QUBOValidator
        
        validator = QUBOValidator()
        
        # 测试样本响应
        sample_response = """
        To convert this problem to QUBO format:
        
        Variables: Let x_1, x_2 be binary variables where x_i ∈ {0,1}
        x_1 represents choice A, x_2 represents choice B
        
        Objective: minimize x_1^2 + 2*x_1*x_2 + x_2^2
        
        Matrix Q = [[1, 1], [1, 1]]
        """
        
        validation = validator.validate_qubo_response(sample_response)
        report = validator.generate_validation_report(validation)
        
        print("验证器测试结果:")
        print(report)
        
        return True
        
    except Exception as e:
        print(f"验证器测试失败: {e}")
        return False

def simulate_experiment_results():
    """模拟实验结果（用于测试分析器）"""
    print("\n=== 模拟实验结果 ===")
    
    problems = load_sample_problems(num_samples=5)
    if not problems:
        return False
    
    # 模拟结果
    simulated_results = []
    for problem in problems:
        # 模拟不同质量的响应
        mock_responses = [
            "This problem cannot be converted to QUBO format.",
            "Variables: x_1, x_2 binary. Objective: minimize x_1 + x_2. No matrix provided.",
            "Let x_i ∈ {0,1} represent binary choices. Minimize x^T Q x where Q = [[1,0],[0,1]]."
        ]
        
        response = random.choice(mock_responses)
        score = random.randint(1, 5)
        
        result = {
            'problem_id': problem['index'],
            'original_question': problem['question'],
            'original_answer': problem['answer'],
            'qubo_response': response,
            'analysis': {
                'format_score': score,
                'content_analysis': 'simulated',
                'mentions_binary': score >= 3,
                'mentions_qubo': score >= 2,
                'has_variables': score >= 2,
                'has_objective': score >= 3,
                'has_matrix': score >= 4
            },
            'generation_time': random.uniform(1.0, 5.0)
        }
        simulated_results.append(result)
    
    # 创建完整的模拟数据
    mock_data = {
        'total_problems': len(simulated_results),
        'high_quality_count': sum(1 for r in simulated_results if r['analysis']['format_score'] >= 4),
        'medium_quality_count': sum(1 for r in simulated_results if 2 <= r['analysis']['format_score'] < 4),
        'low_quality_count': sum(1 for r in simulated_results if r['analysis']['format_score'] < 2),
        'high_quality_rate': 0.0,
        'average_score': sum(r['analysis']['format_score'] for r in simulated_results) / len(simulated_results),
        'average_generation_time': sum(r['generation_time'] for r in simulated_results) / len(simulated_results),
        'detailed_results': simulated_results
    }
    
    mock_data['high_quality_rate'] = mock_data['high_quality_count'] / mock_data['total_problems']
    
    # 保存模拟结果
    mock_file = f"mock_results_{int(time.time())}.json"
    with open(mock_file, 'w', encoding='utf-8') as f:
        json.dump(mock_data, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 模拟结果已保存到: {mock_file}")
    
    # 测试结果分析器
    try:
        from result_analyzer import ResultAnalyzer
        
        analyzer = ResultAnalyzer(mock_file)
        analyzer.print_summary_statistics()
        
        print("✓ 结果分析器测试成功")
        return True
        
    except Exception as e:
        print(f"结果分析器测试失败: {e}")
        return False

def check_model_readiness():
    """检查模型是否准备就绪"""
    print("\n=== 检查模型准备状态 ===")
    
    try:
        import torch
        import transformers
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ Transformers版本: {transformers.__version__}")
        
        # 检查CUDA可用性
        if torch.cuda.is_available():
            print(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
            print(f"  当前设备: {torch.cuda.get_device_name()}")
        else:
            print("⚠ CUDA不可用，将使用CPU")
        
        return True
        
    except ImportError as e:
        print(f"✗ 依赖未安装: {e}")
        return False

def main():
    """主测试函数"""
    print("QUBO转换实验 - 快速测试")
    print("=" * 40)
    
    tests = [
        ("数据加载", test_data_loading),
        ("Prompt生成", test_prompt_generation),
        ("QUBO验证器", test_qubo_validator),
        ("模拟实验", simulate_experiment_results),
        ("模型准备状态", check_model_readiness)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20}")
            result = test_func()
            results[test_name] = result
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"{test_name}: ✗ 异常 - {e}")
    
    print(f"\n{'='*40}")
    print("测试总结:")
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if all(results.values()):
        print("✓ 所有测试通过，实验框架准备就绪！")
    else:
        print("⚠ 部分测试失败，请检查相关组件")
        
        failed_tests = [name for name, result in results.items() if not result]
        print(f"失败的测试: {', '.join(failed_tests)}")

if __name__ == "__main__":
    main()
