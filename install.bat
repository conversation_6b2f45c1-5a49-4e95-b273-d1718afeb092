@echo off
echo QUBO转换实验 - Windows依赖安装脚本
echo ========================================

echo 检查Python...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 选择安装方式:
echo 1. 快速安装 (推荐)
echo 2. 交互式安装
echo 3. 仅安装numpy (测试网络)
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 正在快速安装依赖...
    pip install torch transformers numpy
    if %errorlevel% equ 0 (
        echo 安装成功！
        python check_env.py
    ) else (
        echo 安装失败，请检查网络连接
    )
) else if "%choice%"=="2" (
    echo 启动交互式安装...
    python install_dependencies.py
) else if "%choice%"=="3" (
    echo 测试安装numpy...
    pip install numpy
    if %errorlevel% equ 0 (
        echo numpy安装成功，网络连接正常
        echo 现在可以尝试安装完整依赖
    ) else (
        echo numpy安装失败，请检查网络设置
    )
) else (
    echo 无效选择
)

echo.
echo 按任意键退出...
pause >nul
