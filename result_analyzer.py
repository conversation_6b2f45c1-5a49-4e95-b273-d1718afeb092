#!/usr/bin/env python3
"""
实验结果分析器：分析和展示QUBO转换实验的结果
"""

import json
import os
from typing import Dict, List, Any
from collections import Counter

class ResultAnalyzer:
    """实验结果分析器"""
    
    def __init__(self, result_file: str):
        self.result_file = result_file
        self.data = None
        self.load_results()
    
    def load_results(self):
        """加载实验结果"""
        try:
            with open(self.result_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✓ 成功加载结果文件: {self.result_file}")
        except Exception as e:
            print(f"✗ 加载结果文件失败: {e}")
            self.data = None
    
    def print_summary_statistics(self):
        """打印总结统计"""
        if not self.data:
            print("没有数据可分析")
            return
        
        print("\n" + "="*50)
        print("实验结果总结统计")
        print("="*50)
        
        # 基本统计
        print(f"总问题数: {self.data['total_problems']}")
        print(f"高质量转换: {self.data['high_quality_count']} ({self.data['high_quality_rate']:.1%})")
        print(f"中等质量转换: {self.data['medium_quality_count']}")
        print(f"低质量转换: {self.data['low_quality_count']}")
        print(f"平均质量评分: {self.data['average_score']:.2f}/5")
        print(f"平均生成时间: {self.data['average_generation_time']:.2f}秒")
    
    def analyze_response_patterns(self):
        """分析响应模式"""
        if not self.data or 'detailed_results' not in self.data:
            return
        
        print("\n" + "="*50)
        print("响应模式分析")
        print("="*50)
        
        results = self.data['detailed_results']
        
        # 分析各项指标的通过率
        metrics = {
            'mentions_binary': 0,
            'mentions_qubo': 0,
            'has_variables': 0,
            'has_objective': 0,
            'has_matrix': 0
        }
        
        for result in results:
            analysis = result['analysis']
            for metric in metrics:
                if analysis.get(metric, False):
                    metrics[metric] += 1
        
        total = len(results)
        print("各项指标通过率:")
        for metric, count in metrics.items():
            rate = count / total
            print(f"  {metric}: {count}/{total} ({rate:.1%})")
        
        # 分析评分分布
        scores = [r['analysis']['format_score'] for r in results]
        score_dist = Counter(scores)
        
        print(f"\n评分分布:")
        for score in sorted(score_dist.keys()):
            count = score_dist[score]
            rate = count / total
            print(f"  评分 {score}: {count}个 ({rate:.1%})")
    
    def show_detailed_examples(self, num_examples: int = 3):
        """展示详细示例"""
        if not self.data or 'detailed_results' not in self.data:
            return
        
        print("\n" + "="*50)
        print("详细示例展示")
        print("="*50)
        
        results = self.data['detailed_results']
        
        # 按评分排序，展示最好和最差的例子
        sorted_results = sorted(results, key=lambda x: x['analysis']['format_score'], reverse=True)
        
        print(f"\n--- 最佳转换示例 (前{num_examples}个) ---")
        for i, result in enumerate(sorted_results[:num_examples], 1):
            self._print_example(result, f"最佳示例 {i}")
        
        print(f"\n--- 最差转换示例 (后{num_examples}个) ---")
        for i, result in enumerate(sorted_results[-num_examples:], 1):
            self._print_example(result, f"最差示例 {i}")
    
    def _print_example(self, result: Dict, title: str):
        """打印单个示例"""
        print(f"\n{title}:")
        print(f"问题ID: {result['problem_id']}")
        print(f"原始问题: {result['original_question'][:150]}...")
        print(f"原始答案: {result['original_answer']}")
        print(f"质量评分: {result['analysis']['format_score']}/5")
        print(f"生成时间: {result['generation_time']:.2f}秒")
        print(f"内容分析: {result['analysis']['content_analysis']}")
        
        # 显示QUBO响应的前几行
        response_lines = result['qubo_response'].split('\n')[:5]
        print("QUBO响应预览:")
        for line in response_lines:
            if line.strip():
                print(f"  {line[:100]}...")
        
        print("-" * 40)
    
    def analyze_problem_types(self):
        """分析不同类型问题的转换效果"""
        if not self.data or 'detailed_results' not in self.data:
            return
        
        print("\n" + "="*50)
        print("问题类型分析")
        print("="*50)
        
        results = self.data['detailed_results']
        
        # 简单的问题类型分类（基于关键词）
        problem_types = {
            'algebra': ['equation', 'solve', 'x =', 'polynomial'],
            'geometry': ['triangle', 'circle', 'angle', 'area', 'perimeter'],
            'probability': ['probability', 'chance', 'random', 'dice'],
            'combinatorics': ['combination', 'permutation', 'choose', 'ways'],
            'number_theory': ['prime', 'divisible', 'factor', 'gcd'],
            'optimization': ['maximum', 'minimum', 'optimize', 'best']
        }
        
        type_results = {ptype: [] for ptype in problem_types}
        unclassified = []
        
        for result in results:
            question = result['original_question'].lower()
            classified = False
            
            for ptype, keywords in problem_types.items():
                if any(keyword in question for keyword in keywords):
                    type_results[ptype].append(result['analysis']['format_score'])
                    classified = True
                    break
            
            if not classified:
                unclassified.append(result['analysis']['format_score'])
        
        # 打印各类型的平均表现
        for ptype, scores in type_results.items():
            if scores:
                avg_score = sum(scores) / len(scores)
                print(f"{ptype.capitalize()}: {len(scores)}个问题, 平均评分: {avg_score:.2f}")
        
        if unclassified:
            avg_score = sum(unclassified) / len(unclassified)
            print(f"未分类: {len(unclassified)}个问题, 平均评分: {avg_score:.2f}")
    
    def generate_html_report(self, output_file: str = None):
        """生成HTML报告"""
        if not output_file:
            output_file = self.result_file.replace('.json', '_report.html')
        
        html_content = self._create_html_report()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"✓ HTML报告已生成: {output_file}")
        except Exception as e:
            print(f"✗ 生成HTML报告失败: {e}")
    
    def _create_html_report(self) -> str:
        """创建HTML报告内容"""
        if not self.data:
            return "<html><body><h1>无数据可显示</h1></body></html>"
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>QUBO转换实验报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f0f0f0; padding: 15px; border-radius: 5px; }}
                .example {{ border: 1px solid #ddd; margin: 10px 0; padding: 10px; }}
                .high-quality {{ border-left: 5px solid #4CAF50; }}
                .medium-quality {{ border-left: 5px solid #FF9800; }}
                .low-quality {{ border-left: 5px solid #F44336; }}
                .response {{ background: #f9f9f9; padding: 10px; font-family: monospace; }}
            </style>
        </head>
        <body>
            <h1>QUBO转换实验报告</h1>
            
            <div class="summary">
                <h2>实验总结</h2>
                <p>总问题数: {self.data['total_problems']}</p>
                <p>高质量转换: {self.data['high_quality_count']} ({self.data['high_quality_rate']:.1%})</p>
                <p>中等质量转换: {self.data['medium_quality_count']}</p>
                <p>低质量转换: {self.data['low_quality_count']}</p>
                <p>平均质量评分: {self.data['average_score']:.2f}/5</p>
                <p>平均生成时间: {self.data['average_generation_time']:.2f}秒</p>
            </div>
            
            <h2>详细结果</h2>
        """
        
        if 'detailed_results' in self.data:
            for i, result in enumerate(self.data['detailed_results'], 1):
                score = result['analysis']['format_score']
                quality_class = 'high-quality' if score >= 4 else 'medium-quality' if score >= 2 else 'low-quality'
                
                html += f"""
                <div class="example {quality_class}">
                    <h3>问题 {i} (ID: {result['problem_id']})</h3>
                    <p><strong>原始问题:</strong> {result['original_question']}</p>
                    <p><strong>原始答案:</strong> {result['original_answer']}</p>
                    <p><strong>质量评分:</strong> {score}/5 ({result['analysis']['content_analysis']})</p>
                    <p><strong>生成时间:</strong> {result['generation_time']:.2f}秒</p>
                    <div class="response">
                        <strong>QUBO转换结果:</strong><br>
                        <pre>{result['qubo_response'][:500]}{'...' if len(result['qubo_response']) > 500 else ''}</pre>
                    </div>
                </div>
                """
        
        html += """
            </body>
        </html>
        """
        
        return html
    
    def run_full_analysis(self):
        """运行完整分析"""
        if not self.data:
            print("无法进行分析：数据加载失败")
            return
        
        self.print_summary_statistics()
        self.analyze_response_patterns()
        self.analyze_problem_types()
        self.show_detailed_examples(3)
        
        # 生成HTML报告
        self.generate_html_report()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        result_file = sys.argv[1]
    else:
        # 查找最新的结果文件
        result_files = [f for f in os.listdir('.') if f.startswith('qubo_experiment_results_') and f.endswith('.json')]
        if result_files:
            result_file = max(result_files, key=lambda x: os.path.getctime(x))
            print(f"使用最新的结果文件: {result_file}")
        else:
            print("未找到结果文件，请先运行实验")
            return
    
    analyzer = ResultAnalyzer(result_file)
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
