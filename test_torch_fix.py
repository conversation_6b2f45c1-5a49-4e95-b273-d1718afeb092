#!/usr/bin/env python3
"""
测试torch导入修复
"""

def test_imports():
    """测试导入"""
    print("=== 测试导入 ===")
    
    try:
        from qubo_experiment import QUBOExperiment, TORCH_AVAILABLE
        print("✓ QUBOExperiment导入成功")
        print(f"✓ TORCH_AVAILABLE: {TORCH_AVAILABLE}")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        from qubo_experiment import QUBOExperiment
        
        experiment = QUBOExperiment()
        print("✓ 实验对象创建成功")
        
        # 尝试加载模型
        success = experiment.load_model()
        if success:
            print("✓ 模型加载成功")
            return True
        else:
            print("✗ 模型加载失败")
            return False
            
    except Exception as e:
        print(f"✗ 模型加载测试失败: {e}")
        return False

def test_simple_generation():
    """测试简单生成"""
    print("\n=== 测试简单生成 ===")
    
    try:
        from qubo_experiment import QUBOExperiment
        
        experiment = QUBOExperiment()
        
        # 加载模型
        if not experiment.load_model():
            print("✗ 模型加载失败，跳过生成测试")
            return False
        
        # 测试简单的prompt
        test_prompt = "Convert this simple problem to QUBO: Find the minimum of x + y where x, y are binary variables."
        
        print("正在测试生成...")
        response = experiment.generate_response(test_prompt, max_length=100)
        
        print(f"生成响应长度: {len(response)}")
        print(f"响应预览: {response[:200]}...")
        
        # 检查是否是错误消息
        if response.startswith("生成失败:"):
            print(f"✗ 生成失败: {response}")
            return False
        else:
            print("✓ 生成成功")
            return True
            
    except Exception as e:
        print(f"✗ 生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Torch导入修复测试")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_imports),
        ("模型加载测试", test_model_loading),
        ("简单生成测试", test_simple_generation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name}异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 40)
    print("测试总结:")
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if all(results.values()):
        print("🎉 所有测试通过！现在可以运行完整实验了")
        print("运行命令: python qubo_experiment.py")
    else:
        print("⚠ 部分测试失败，请检查相关问题")

if __name__ == "__main__":
    main()
